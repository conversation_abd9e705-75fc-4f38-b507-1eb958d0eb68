# Demo Common Cache 開發環境配置
# 注意：Redis 基本連線配置（host, port, username, password）在主配置文件中設置
# 這裡只配置開發環境特定的設置

spring:
  # Redis 基本配置 - 開發環境特定設置
  data:
    redis:
      # 不覆蓋主配置中的 host, port, username, password
      # 只設置開發環境特定的參數
      timeout: 5000ms                     # 開發環境使用較長的超時時間

# Common Cache 開發環境配置
common:
  cache:
    # Redis 連線進階配置 - 開發環境
    redis:
      connection-pool:
        pool-size: 32                     # 測試需要 32 個連線池大小
        minimum-idle-size: 5              # 測試需要 5 個最小空閒連線
        idle-connection-timeout: 10000    # 測試需要 10000ms 空閒連線超時
      timeout:
        connect-timeout: 10000            # 測試需要 10000ms 連線超時
        command-timeout: 3000             # 測試需要 3000ms 命令超時
      retry:
        attempts: 3                       # 測試需要 3 次重試
        interval: 1500                    # 測試需要 1500ms 重試間隔
      thread-pool:
        threads: 8                        # 測試需要 8 個執行緒
        netty-threads: 16                 # 測試需要 16 個 Netty 執行緒
      misc:
        keep-alive: true                  # 是否啟用 Keep Alive
        tcp-no-delay: true                # 是否啟用 TCP No Delay
    
    # Redis 環境保護設定 - 開發環境
    env-guard:
      enabled: true
      production-profiles: 
        - prod
        - production
      dangerous-operations-enabled: true  # 開發環境允許危險操作
    
    # Session 管理配置 - 開發環境
    session:
      default-ttl: 1800                   # 測試需要 1800 秒 TTL
      key-prefix: "demo:session"
      cleanup-interval: 60                # 1 分鐘清理一次
      auto-renewal: true
      renewal-threshold: 0.3              # 剩餘 30% TTL 時自動續期
    
    # 快取管理配置 - 開發環境
    cache:
      default-ttl: 3600                   # 測試需要 3600 秒 TTL
      key-prefix: "demo:cache"
      auto-renewal: true
      renewal-threshold: 0.3              # 剩餘 30% TTL 時自動續期
      max-size: 1000                      # 開發環境較小的快取容量
    
    # 鎖管理配置 - 開發環境
    lock:
      default-lease-time: 30              # 測試需要 30 秒租約時間
      default-wait-time: 5                # 開發環境較短的等待時間
      key-prefix: "demo:lock"
      fair-lock-enabled: true
    
    # 清理器配置 - 開發環境
    cleaner:
      enabled: true
      schedule-interval: 300              # 測試需要 300 秒間隔
      batch-size: 100                     # 開發環境較小的批次大小
      expired-key-scan-count: 50          # 較少的過期 Key 掃描數量

# 日誌配置 - 開發環境
logging:
  level:
    com.nanshan: DEBUG                    # 開發環境使用 DEBUG 級別
    org.redisson: DEBUG
    org.springframework.data.redis: DEBUG
    io.lettuce.core: DEBUG               # 開發環境顯示詳細的 Redis 連線日誌
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
