package com.nanshan.common.cache.config;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.TimeZone;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.Config;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Redisson 配置類
 *
 * 建立和配置 RedissonClient Bean
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableConfigurationProperties({RedisProperties.class})
public class RedissonConfig {

    private static final Logger log = LoggerFactory.getLogger(RedissonConfig.class);

    private final RedisProperties redisProperties;

    public RedissonConfig(RedisProperties redisProperties) {
        this.redisProperties = redisProperties;
    }

    /**
     * 建立 RedissonClient Bean
     *
     * @return RedissonClient 實例
     */
    @Bean
    @ConditionalOnMissingBean(RedissonClient.class)
    public RedissonClient redissonClient() {
        Config config = new Config();

        // 配置單機模式
        configureSingleServer(config);

        // 配置強化的 Jackson 編解碼器
        ObjectMapper objectMapper = createRobustObjectMapper();
        config.setCodec(new JsonJacksonCodec(objectMapper));

        log.info("Redisson 客戶端配置成功，已啟用增強的 JSON 解析");
        return Redisson.create(config);
    }

    /**
     * 創建強化的 ObjectMapper，能夠處理控制字符
     *
     * @return 配置完善的 ObjectMapper
     */
    private ObjectMapper createRobustObjectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 支援 Java 8 時間 API
        objectMapper.registerModule(new JavaTimeModule());

        // 配置 JSON 解析特性以處理控制字符 - 這是解決問題的關鍵
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
        objectMapper.configure(JsonParser.Feature.ALLOW_BACKSLASH_ESCAPING_ANY_CHARACTER, true);

        // 配置反序列化特性以提高容錯性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 配置序列化特性
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);

        // 設置時區
        objectMapper.setTimeZone(TimeZone.getDefault());

        log.debug("創建強化的 ObjectMapper，支援控制字符處理");
        return objectMapper;
    }

    /**
     * 配置單機 Redis 伺服器
     *
     * @param config Redisson 配置
     */
    private void configureSingleServer(Config config) {
        String address = String.format("redis://%s:%d",
                redisProperties.getHost(), redisProperties.getPort());

        // 計算超時時間（毫秒）
        int timeoutMs = (int) redisProperties.getTimeout().toMillis();

        // 處理資料庫索引，如果未設置則使用預設值 0
        Integer database = redisProperties.getDatabase();
        int databaseIndex = (database != null) ? database : 0;

        log.info("配置 Redis 連線參數:");
        log.info("  地址: {}", address);
        log.info("  資料庫: {} (原始值: {})", databaseIndex, database);
        log.info("  用戶名: {}", redisProperties.getUsername());
        log.info("  密碼: {}", redisProperties.getPassword() != null ? "[已設置]" : "[未設置]");
        log.info("  超時時間: {}ms", timeoutMs);

        var singleServerConfig = config.useSingleServer()
                .setAddress(address)
                .setConnectionPoolSize(10)
                .setConnectionMinimumIdleSize(2)
                .setConnectTimeout(timeoutMs)
                .setTimeout(timeoutMs);

        // 設置資料庫索引（如果未配置則使用預設值 0）
        singleServerConfig.setDatabase(databaseIndex);
        log.info("設置 Redis 資料庫索引: {}", databaseIndex);

        // 只有在密碼不為空時才設置密碼
        if (redisProperties.getPassword() != null && !redisProperties.getPassword().trim().isEmpty()) {
            singleServerConfig.setPassword(redisProperties.getPassword());
            log.info("已設置 Redis 密碼");
        }

        // 只有在用戶名不為空時才設置用戶名
        if (redisProperties.getUsername() != null && !redisProperties.getUsername().trim().isEmpty()) {
            singleServerConfig.setUsername(redisProperties.getUsername());
            log.info("已設置 Redis 用戶名: {}", redisProperties.getUsername());
        }

        log.info("Redisson 單機伺服器配置完成: {}", address);
    }
}
