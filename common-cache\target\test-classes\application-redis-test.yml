# Redis 連線測試配置
spring:
  application:
    name: common-cache-redis-test
  
  # Redis 基本配置 - 使用您的 Redis 伺服器
  data:
    redis:
      host: *************
      port: 8085
      username: user 
      password: pass 
      database: 0
      timeout: 5000ms
      # 連線池配置
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 2
          max-wait: 3000ms
        shutdown-timeout: 100ms

# Common Cache 配置
common:
  cache:
    # Redis 環境保護設定 - 測試環境允許危險操作
    env-guard:
      enabled: true
      production-profiles: 
        - prod
        - production
      dangerous-operations-enabled: true
    
    # Session 管理配置
    session:
      default-ttl: 300  # 5 分鐘測試
      key-prefix: "test:session"
      cleanup-interval: 60
    
    # 快取管理配置
    cache:
      default-ttl: 600  # 10 分鐘測試
      key-prefix: "test:cache"
      auto-renewal: true
      renewal-threshold: 0.5
    
    # 鎖管理配置
    lock:
      default-lease-time: 30
      default-wait-time: 10
      key-prefix: "test:lock"
      fair-lock-enabled: true
    
    # 清理器配置
    cleaner:
      enabled: false  # 測試時關閉清理器

# 日誌配置 - 詳細日誌用於診斷
logging:
  level:
    com.nanshan.common.cache: DEBUG
    org.redisson: DEBUG
    org.springframework.data.redis: DEBUG
    io.lettuce: DEBUG
    io.netty: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
