package com.nanshan.common.cache;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * Redis Properties 預設值測試
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class RedisPropertiesTest {

    @Autowired
    private RedisProperties redisProperties;

    @Test
    public void testRedisPropertiesDefaults() {
        log.info("=== Redis Properties 預設值測試 ===");
        
        log.info("Host: {}", redisProperties.getHost());
        log.info("Port: {}", redisProperties.getPort());
        log.info("Database: {}", redisProperties.getDatabase());
        log.info("Username: '{}'", redisProperties.getUsername());
        log.info("Password: '{}'", redisProperties.getPassword());
        log.info("Timeout: {}", redisProperties.getTimeout());
        
        // 檢查 database 是否為 null
        Integer database = redisProperties.getDatabase();
        log.info("Database 是否為 null: {}", database == null);
        log.info("Database 值: {}", database);
        
        // 檢查其他可能為 null 的屬性
        log.info("Username 是否為 null: {}", redisProperties.getUsername() == null);
        log.info("Password 是否為 null: {}", redisProperties.getPassword() == null);
        log.info("Timeout 是否為 null: {}", redisProperties.getTimeout() == null);
    }
}
