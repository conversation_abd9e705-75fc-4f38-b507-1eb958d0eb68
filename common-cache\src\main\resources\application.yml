# Common Cache 模組配置
spring:
  application:
    name: common-cache
  
  # Redis 基本配置
  data:
    redis:
      host: *************
      port: 8085
      username: user
      password: pass
      database: 0
      timeout: 5000ms

# Common Cache 自訂配置
common:
  cache:
    # Redis 環境保護設定
    env-guard:
      enabled: true
      production-profiles: 
        - prod
        - production
      dangerous-operations-enabled: false
    
    # Session 管理配置
    session:
      default-ttl: 1800  # 30 分鐘 (秒)
      key-prefix: "session"
      cleanup-interval: 300  # 5 分鐘清理一次 (秒)
    
    # 快取管理配置  
    cache:
      default-ttl: 3600  # 1 小時 (秒)
      key-prefix: "cache"
      auto-renewal: true
      renewal-threshold: 0.2  # 剩餘 20% TTL 時自動續期
    
    # 鎖管理配置
    lock:
      default-lease-time: 30  # 30 秒 (秒)
      default-wait-time: 10   # 10 秒 (秒)
      key-prefix: "lock"
      fair-lock-enabled: true
    
    # 清理器配置
    cleaner:
      enabled: true
      schedule-interval: 600  # 10 分鐘執行一次 (秒)
      batch-size: 1000
      expired-key-scan-count: 100

# 日誌配置
logging:
  level:
    com.nanshan.common.cache: DEBUG
    org.redisson: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"

---
# 開發環境配置
spring:
  config:
    activate:
      on-profile: dev
  data:
    redis:
      host: localhost
      port: 6379

common:
  cache:
    env-guard:
      dangerous-operations-enabled: true

---
# 測試環境配置
spring:
  config:
    activate:
      on-profile: test
  data:
    redis:
      host: localhost
      port: 6379

---
# 生產環境配置
spring:
  config:
    activate:
      on-profile: prod
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      username: ${REDIS_USERNAME:}
      password: ${REDIS_PASSWORD:}

common:
  cache:
    env-guard:
      dangerous-operations-enabled: false
    session:
      default-ttl: 3600  # 生產環境 1 小時
    cleaner:
      schedule-interval: 300  # 生產環境 5 分鐘清理一次
