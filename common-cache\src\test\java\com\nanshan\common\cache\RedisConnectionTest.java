package com.nanshan.common.cache;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Redis 連線診斷測試
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
public class RedisConnectionTest {

    @Test
    public void testDirectRedisConnection() {
        log.info("開始測試直接 Redis 連線...");
        
        // 測試配置
        String host = "*************";
        int port = 8085;
        String username = "user";
        String password = "pass";
        int database = 0;
        
        Config config = new Config();
        String address = String.format("redis://%s:%d", host, port);
        
        var singleServerConfig = config.useSingleServer()
                .setAddress(address)
                .setDatabase(database)
                .setConnectionPoolSize(5)
                .setConnectionMinimumIdleSize(1)
                .setConnectTimeout(5000)
                .setTimeout(5000);
        
        // 設置認證信息
        if (username != null && !username.trim().isEmpty()) {
            singleServerConfig.setUsername(username);
            log.info("設置用戶名: {}", username);
        }
        
        if (password != null && !password.trim().isEmpty()) {
            singleServerConfig.setPassword(password);
            log.info("設置密碼: [已隱藏]");
        }
        
        log.info("嘗試連接到 Redis: {}", address);
        log.info("資料庫: {}", database);
        
        RedissonClient redissonClient = null;
        try {
            redissonClient = Redisson.create(config);
            log.info("Redisson 客戶端創建成功");
            
            // 測試連線
            var bucket = redissonClient.getBucket("test:connection");
            bucket.set("test-value");
            String value = (String) bucket.get();
            
            log.info("連線測試成功！設置和獲取值: {}", value);
            
            // 清理測試數據
            bucket.delete();
            log.info("清理測試數據完成");
            
        } catch (Exception e) {
            log.error("Redis 連線失敗", e);
            log.error("錯誤類型: {}", e.getClass().getSimpleName());
            log.error("錯誤訊息: {}", e.getMessage());
            
            if (e.getCause() != null) {
                log.error("根本原因: {}", e.getCause().getMessage());
            }
            
            // 打印詳細的堆疊追蹤
            e.printStackTrace();
            
        } finally {
            if (redissonClient != null) {
                try {
                    redissonClient.shutdown();
                    log.info("Redisson 客戶端已關閉");
                } catch (Exception e) {
                    log.warn("關閉 Redisson 客戶端時發生錯誤", e);
                }
            }
        }
    }
    
    @Test
    public void testRedisConnectionWithDifferentConfigs() {
        log.info("測試不同的 Redis 連線配置...");
        
        String host = "*************";
        int port = 8085;
        String username = "user";
        String password = "pass";
        
        // 測試配置 1: 不指定資料庫
        testConnectionConfig(host, port, username, password, null, "配置1 - 不指定資料庫");
        
        // 測試配置 2: 指定資料庫 0
        testConnectionConfig(host, port, username, password, 0, "配置2 - 資料庫 0");
        
        // 測試配置 3: 指定資料庫 1
        testConnectionConfig(host, port, username, password, 1, "配置3 - 資料庫 1");
        
        // 測試配置 4: 不使用用戶名
        testConnectionConfig(host, port, null, password, 0, "配置4 - 僅密碼");
        
        // 測試配置 5: 不使用認證
        testConnectionConfig(host, port, null, null, 0, "配置5 - 無認證");
    }
    
    private void testConnectionConfig(String host, int port, String username, String password, Integer database, String configName) {
        log.info("=== {} ===", configName);
        
        Config config = new Config();
        String address = String.format("redis://%s:%d", host, port);
        
        var singleServerConfig = config.useSingleServer()
                .setAddress(address)
                .setConnectionPoolSize(2)
                .setConnectionMinimumIdleSize(1)
                .setConnectTimeout(3000)
                .setTimeout(3000);
        
        if (database != null) {
            singleServerConfig.setDatabase(database);
        }
        
        if (username != null && !username.trim().isEmpty()) {
            singleServerConfig.setUsername(username);
        }
        
        if (password != null && !password.trim().isEmpty()) {
            singleServerConfig.setPassword(password);
        }
        
        RedissonClient redissonClient = null;
        try {
            redissonClient = Redisson.create(config);
            
            // 簡單的連線測試
            var bucket = redissonClient.getBucket("test:" + System.currentTimeMillis());
            bucket.set("test");
            String value = (String) bucket.get();
            bucket.delete();
            
            log.info("{} - 連線成功！", configName);
            
        } catch (Exception e) {
            log.error("{} - 連線失敗: {}", configName, e.getMessage());
        } finally {
            if (redissonClient != null) {
                try {
                    redissonClient.shutdown();
                } catch (Exception e) {
                    // 忽略關閉錯誤
                }
            }
        }
    }
}
